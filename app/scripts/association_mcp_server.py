import uvicorn
import json
from mcp.server.sse import SseServerTransport
from mcp.server import Server
from mcp.types import Tool, TextContent

from starlette.applications import Starlette
from starlette.routing import Route, Mount
from src.utils.utils import get_file_from_url
from src.processors.data_process_2 import annual_data_process, monthly_data_extract, fill_associations
from scripts.query import extract_task_progress

app = Server("guowang_sse_server")

@app.list_tools()
async def list_tools() -> list[Tool]:
    """列出可用的工具

    Returns:
        list[Tool]: 工具列表
    """
    return [
        Tool(
            name="fill_association",
            description="将关联信息回填到中间表中",
            inputSchema={
                "type": "object",
                "properties": {"assoc_list": {"type": "list", "description": "关联信息列表"},
                               "month": {"type": "stirng", "description": "月度计划对应的月份"},
                               "source_name":{"type": "string", "description": "月度计划任务来源名称"}
                               },
                "required": ["assoc_list", "month", "source_name"],
            }
        ),
        Tool(
            name="annual_data_process",
            description="将年度重点工作任务转换为中间表",
            inputSchema={
                "type": "object",
                "properties": {"annual_input_url": {"type": "string", "description": "年度重点工作任务URL"}},
                "required": ["annual_input_url"],
            }
        ),
        Tool(
            name="monthly_data_extract",
            description="按部门提取月度计划数据和年度重点工作任务数据",
            inputSchema={
                "type": "object",
                "properties": {"monthly_input_url": {"type": "string", "description": "月度计划URL"}},
                "required": ["monthly_input_url"],
            }
        ),
        Tool(
            name="monthly_reports_extract",
            description="按部门提取月报数据和中间表工作任务数据",
            inputSchema={
                "type": "object",
                "properties": {"reports_input_url": {"type": "list", "description": "月报URL"}},
                "required": ["reports_input_url"],
            }
        ),
    ]

@app.call_tool()
async def call_tool(name: str, arguments: dict) -> list[TextContent]:
    if name == "fill_association":
        assoc_list = arguments.get("assoc_list")
        month = arguments.get("month")
        if month:
            month = int(month)
        source_name = arguments.get("source_name")
        print("-----------------------------------------------------")
        print("source_name:", source_name)
        print("assoc_list:", assoc_list)
        if (not assoc_list) or (not month) or (not source_name):
            raise ValueError("缺少输入")
        return fill_associations(assoc_list, month, source_name)
    elif name == "annual_data_process":
        annual_input_url = arguments.get("annual_input_url")
        if annual_input_url:
            file_path, uuid = get_file_from_url(annual_input_url, 'a')
            return annual_data_process(file_path)
        else:
            return [TextContent(type="text", text=f"年度重点工作任务URL不存在")]
    elif name == "monthly_data_extract":
        monthly_input_url = arguments.get("monthly_input_url")
        # for u in monthly_input_url:
        if monthly_input_url:
            file_path, uuid = get_file_from_url(monthly_input_url, 'm')
            return monthly_data_extract()
        else:
            return [TextContent(type="text", text=f"月度计划URL不存在")]
    elif name == "monthly_data_extract":
        monthly_input_url = arguments.get("monthly_input_url")
        # for u in monthly_input_url:
        if monthly_input_url:
            file_path, uuid = get_file_from_url(monthly_input_url, 'm')
            return monthly_data_extract()
        else:
            return [TextContent(type="text", text=f"月度计划URL不存在")]
    elif name == "monthly_reports_extract":
        monthly_input_url = arguments.get("monthly_input_url")
        # for u in monthly_input_url:
        if monthly_input_url:
            for url in monthly_input_url:
                print("Processing URL:", url)
                file_path, uuid = get_file_from_url(monthly_input_url, 'm')
            return extract_task_progress()
        else:
            return [TextContent(type="text", text=f"月报URL不存在")]
    else:
        return [TextContent(type="text", text=f"未知工具: {name}")]

sse = SseServerTransport("/messages/")

# Handler for SSE connections
async def handle_sse(request):
    async with sse.connect_sse(
        request.scope, request.receive, request._send
    ) as streams:
        await app.run(streams[0], streams[1], app.create_initialization_options())
        
# Create Starlette app with routes
starlette_app = Starlette(
    debug=True,
    routes=[
        Route("/sse", endpoint=handle_sse),
        Mount("/messages/", app=sse.handle_post_message),
    ],
)


if __name__ == "__main__":
    uvicorn.run(starlette_app, host="0.0.0.0", port=9001)