beautifulsoup4==4.13.4
blinker==1.9.0
bs4==0.0.2
camelot-py==1.0.0
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
cryptography==45.0.3
docx==0.2.4
et_xmlfile==2.0.0
Flask==3.1.1
Flask-SQLAlchemy==3.1.1
ghostscript==0.7
greenlet==3.2.2
html2docx==1.6.0
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
lxml==5.4.0
Markdown==3.8
MarkupSafe==3.0.2
numpy==2.2.6
opencv-python-headless==*********
openpyxl==3.1.5
pandas==2.2.3
pdfminer.six==20250506
pillow==11.2.1
pycparser==2.22
PyMuPDF==1.26.0
pypdf>=4.0,<6.0
pypdfium2==4.30.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
pytz==2025.2
requests==2.32.3
six==1.17.0
soupsieve==2.7
SQLAlchemy==2.0.41
tabulate==0.9.0
tinycss2==1.4.0
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
webencodings==0.5.1
Werkzeug==3.1.3
