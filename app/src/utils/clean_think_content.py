from bs4 import BeautifulSoup

'''
如果是带有思考过程的内容返回true，不带思考过程的返回false
'''
def validate_think_type_model(html):
    return validate_think_tags(html) or validate_details_tags(html)
'''
验证是否含有思考过程，平台版，如果有思考过程，返回true，没有返回false
'''
def validate_details_tags(html):
    stack = []
    i = 0
    flag=0
    while i < len(html):
        if html[i:i+8] == "<details":
            stack.append("<details>")
            i += 8
            flag=1
        elif html[i:i+10] == "</details>":
            if len(stack) == 0:
                return False  # 遇到不匹配的结束标签
            stack.pop()
            i += 10
        else:
            i += 1
    return len(stack) == 0 and flag!=0 # 栈为空表示所有标签匹配
'''
验证是否含有思考过程，原生版，如果有思考过程，返回true，没有返回false
'''
def validate_think_tags(html):
    stack = []
    i = 0
    flag=0
    while i < len(html):
        if html[i:i+6] == "<think":
            stack.append("<think>")
            i += 6
            flag=1
        elif html[i:i+8] == "</think>":
            if len(stack) == 0:
                return False  # 遇到不匹配的结束标签
            stack.pop()
            i += 8
        else:
            i += 1
    return len(stack) == 0  and flag!=0 # 栈为空表示所有标签匹配

def clean_think_content(text):
    if text.startswith('<details'):
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(text, 'html.parser')

        summary_tag=soup.find('summary')
        summary_tag.decompose()

        # 找到最外层的<details>标签
        outer_details = soup.find('details')

        # 提取思考过程（去除最外层的<details>标签）
        think_content = str(outer_details.decode_contents()).strip()  # decode_contents()移除标签但保留内容
        outer_details.decompose()
        result_content=str(soup).strip()
        # 创建JSON对象
        return think_content,result_content

    else:
        think_content=None
        result_content=text
        return think_content,result_content

def clean_think_content_think_type(text):
    if text.startswith('<think>'):
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(text, 'html.parser')

        # summary_tag=soup.find('summary')
        # summary_tag.decompose()

        # 找到最外层的<details>标签
        outer_details = soup.find('think')

        # 提取思考过程（去除最外层的<details>标签）
        think_content = str(outer_details.decode_contents()).strip()  # decode_contents()移除标签但保留内容
        outer_details.decompose()
        result_content=str(soup).strip()
        # 创建JSON对象
        return think_content,result_content

    else:
        think_content=None
        result_content=text
        return think_content,result_content

# deepseek_output = """
# <details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>
# 好的，我现在需要处理用户提供的JSON数据，从中提取“data”字段，并返回其值返回是<details>哈哈哈</details>。
# </details>
#
# 返回是<details>哈哈哈</details>
# """