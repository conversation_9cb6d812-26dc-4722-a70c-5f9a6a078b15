import os
import re
import json
import shutil

import camelot
import fitz  # PyMuPDF
import pandas as pd
from pathlib import Path
from openpyxl import load_workbook
from openpyxl.styles import Alignment
from flask import Blueprint
from flask import request, jsonify
from flask import send_file
from flask import request
from src.utils.clean_think_content import validate_think_type_model, clean_think_content, clean_think_content_think_type, validate_details_tags, validate_think_tags
from src.utils.utils import get_file_from_url
import markdown
from html2docx import html2docx

# 获取父目录
parent_dir = Path(__file__).parent

blueprint = Blueprint('mainController', __name__, url_prefix='/unicomapi')


@blueprint.route("/stepB", methods=['GET'])
def test():
    print("接收到stepB的get请求")
    return {
        "message": "stepB 接收到get请求"
    }


def extract_important_works(excel_path, sheet_name='工作任务'):
    df = pd.read_excel(excel_path, sheet_name=sheet_name, skiprows=1)
    important_works = df['重点工作'].drop_duplicates().tolist()
    return important_works


def extract_department_name(pdf_path):
    # Open the PDF file and extract text from the first page
    try:
        doc = fitz.open(pdf_path)
        if not doc:
            print("Failed to open PDF.")
            return None

        page = doc.load_page(0)  # Load the first page
        text = page.get_text("text")

        # Use regular expression to find the department name
        match = re.search(r'(.+?)[周|月][度|例]会报表', text)
        if match:
            return match.group(1)
        else:
            print("Department name not found in the PDF header.")
            return None
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None


def extract_meeting_report(pdf_path):
    # Extract tables from PDF using camelot
    try:
        tables = camelot.read_pdf(pdf_path, pages='all')
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return [], []

    all_indexes = []
    all_tasks = []
    all_task_statuses = []

    for table in tables:
        df = table.df
        if not df.empty:
            header_row = df.iloc[0].tolist()

            index_col_index = None
            task_col_index = None
            status_col_index = None

            # Identify column indices for 任务名称 and 任务开展情况
            for col_idx, col_header in enumerate(header_row):
                if '序号' in str(col_header):
                    index_col_index = col_idx
                if '任务名称' in str(col_header):
                    task_col_index = col_idx
                if '任务开展情况' in str(col_header):
                    status_col_index = col_idx

            if index_col_index is not None:
                indexes = df.iloc[1:, index_col_index].fillna('').astype(str).tolist()
                indexes = [idx.replace('\n', '') for idx in indexes]
                all_indexes.extend(indexes)
            else:
                indexes = []

            # Extract tasks and statuses from current table
            if task_col_index is not None:
                tasks = df.iloc[1:, task_col_index].fillna('').astype(str).tolist()
                tasks = [task.replace('\n', '') for task in tasks]
                all_tasks.extend(tasks)
            else:
                tasks = []

            if status_col_index is not None:
                task_statuses = df.iloc[1:, status_col_index].fillna('').astype(str).tolist()
                task_statuses = [status.replace('\n', '') for status in task_statuses]
                all_task_statuses.extend(task_statuses)

    # 清理逻辑：处理 all_indexes 中的空字符串
    cleaned_indexes = []
    cleaned_tasks = []
    cleaned_statuses = []

    for idx, task, status in zip(all_indexes, all_tasks, all_task_statuses):
        if idx == "":
            if cleaned_tasks:  # 如果有上一个任务，合并
                cleaned_tasks[-1] += task
                cleaned_statuses[-1] += status
        else:
            cleaned_indexes.append(idx)
            cleaned_tasks.append(task)
            cleaned_statuses.append(status)

    return cleaned_tasks, cleaned_statuses


def check_missing_items(department_tasks, important_works):
    missing_data = []
    # missing_items = [work for work in important_works if work not in department_tasks]
    for work in important_works:
        if work not in department_tasks:
            missing_data.append({
                "漏项任务名称": work
            })
    if len(missing_data) == 0:
        missing_data.append({'漏项任务名称': '无'})
    return missing_data


def analyze_task_status_descriptions(tasks, task_statuses, minimum=20):
    # ambiguous_terms = ['正在进行中', '待定', '计划中']
    # ambiguous_statuses = {}
    vague_data = []
    for task, status in zip(tasks, task_statuses):
        # if len(status) < minimum or any(term in status for term in ambiguous_terms):
        is_vague_flag, reason = is_vague(status, minimum)
        if is_vague_flag:
            # ambiguous_statuses[task] = status
            vague_data.append({
                "任务名称": task,
                "任务开展情况": status,
                "是否含糊": is_vague_flag,
                "原因": reason
            })
    if len(vague_data) == 0:
        vague_data.append({'任务名称': '无'})
    return vague_data


# 判断函数：判断描述是否含糊
def is_vague(description, min_len=10, keywords=None):
    description = str(description).strip()
    if not description:
        return True, "无描述内容"

    # 判断字数是否过短
    if len(description) <= min_len:
        return True, f"字数过短（{len(description)}个字）"

    # 判断是否包含模糊术语
    if not keywords:
        keywords = ["进行中", "推进中", "计划中", "按计划进行", "正常推进", "准备中"]

    for keyword in keywords:
        if keyword in description:
            if description == keyword:  # 仅含模糊术语
                return True, f"仅含模糊术语 '{keyword}'"
            elif len(description) <= 20:  # 短描述中含术语
                return True, f"短描述中含模糊术语 '{keyword}'"

    return False, ""


# 生成包含两个工作表的报告文件
def generate_report(data, output_file, sheet_name='sheet1'):
    # 使用 ExcelWriter 写入多个工作表
    with pd.ExcelWriter(output_file) as writer:
        pd.DataFrame(data).to_excel(writer, sheet_name=sheet_name, index=False)
    adjust_format(output_file)


def adjust_format(output_file):
    # 设置列宽和行高
    wb = load_workbook(output_file)

    for ws in wb.worksheets:
        # 自动换行
        for row in ws.iter_rows():
            for cell in row:
                cell.alignment = Alignment(wrap_text=True)

        # 调整列宽
        for col in ws.columns:
            max_length = 0
            column_letter = col[0].column_letter  # 获取列字母
            for cell in col:
                try:
                    if cell.value:
                        current_length = len(str(cell.value))
                        if current_length > max_length:
                            max_length = current_length
                except:
                    pass
            # 设置列宽（字符宽度 * 1.2）
            adjusted_width = max_length * 1.2
            ws.column_dimensions[column_letter].width = adjusted_width

        # 调整行高
        for row in ws.iter_rows():
            max_lines = 0
            row_num = row[0].row
            for cell in row:
                if cell.value:
                    lines = str(cell.value).count('\n') + 1
                    if lines > max_lines:
                        max_lines = lines
            # 设置行高（行数 * 15）
            ws.row_dimensions[row_num].height = max_lines * 15

    # 保存修改
    wb.save(output_file)


def add_sheet_to_excel(data, file_path, sheet_name='sheet1', adjust_columns=True, adjust_rows=True, wrap_text=True):
    """
    向已有 Excel 文件中添加新的工作表

    参数:
    - file_path: Excel 文件路径
    - sheet_name: 新工作表名称
    - data: 要写入的数据（pandas DataFrame）
    - adjust_columns: 是否自动调整列宽
    - wrap_text: 是否启用自动换行
    """
    if not os.path.isfile(file_path):
        generate_report(data, file_path, sheet_name)
        print(f"已成功创建文件 '{file_path}' ")
        print(f"工作表 '{sheet_name}' 已成功添加到文件 '{file_path}' 中")
        return

        # 1. 使用 ExcelWriter 以追加模式打开文件
    with pd.ExcelWriter(file_path, mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
        # 2. 写入新工作表
        pd.DataFrame(data).to_excel(writer, sheet_name=sheet_name, index=False)

        # 3. 获取工作表对象
        wb = writer.book
        ws = wb[sheet_name]

        # 4. 自动换行（如果启用）
        if wrap_text:
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True)

        # 5. 自动调整列宽（如果启用）
        if adjust_columns:
            for col in ws.columns:
                max_length = 0
                column_letter = col[0].column_letter  # 获取列字母
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                # 设置列宽（字符宽度 * 1.2，增加缓冲）
                adjusted_width = max_length * 1.2
                ws.column_dimensions[column_letter].width = adjusted_width

        # 6. 调整行高
        if adjust_rows:
            for row in ws.iter_rows():
                max_lines = 0
                row_num = row[0].row
                for cell in row:
                    if cell.value:
                        lines = str(cell.value).count('\n') + 1
                        if lines > max_lines:
                            max_lines = lines
                # 设置行高（行数 * 15）
                ws.row_dimensions[row_num].height = max_lines * 15

    print(f"工作表 '{sheet_name}' 已成功添加到文件 '{file_path}' 中")


def save_json(data, output_path):
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with open(os.path.join(output_path, 'data.json'), 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


def load_json(json_path):
    try:
        with open(os.path.join(json_path, 'data.json'), 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：文件 {json_path} 不存在")
        return '空'
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败：{e}")
        return '空'


def find_latest_folder(path):
    max_number = -1
    max_folder = None

    # 检查路径是否存在
    if not os.path.exists(path):
        print(f"路径 {path} 不存在")
    else:
        # 遍历路径下的所有条目
        for entry in os.listdir(path):
            full_path = os.path.join(path, entry)
            # 检查是否为文件夹且名称由纯数字组成
            if os.path.isdir(full_path) and entry.isdigit():
                number = int(entry)
                # 更新最大数字和对应的文件夹路径
                if number > max_number:
                    max_number = number
                    max_folder = full_path
    return max_folder


def remove_root(path, root):
    """
    移除路径中的根目录部分
    """
    # 确保根目录有正确的格式（添加结尾分隔符）
    if not root.endswith(os.sep):
        root += os.sep

    # 检查路径是否以根目录开头
    if path.startswith(root):
        return os.path.relpath(path, root)

    # 如果不以根目录开头，返回原始路径或根据需求处理
    return path  # 或者返回 None/抛出异常


def get_previous_data(department_name, month, week):
    root_path = os.path.join(parent_dir, 'history', department_name)
    if week == 0:
        # 获取本部门上月任务列表和任务描述列表
        previous_data_path = os.path.join(root_path, str(month - 1))
    elif week == 1:
        # 获取本部门上周任务列表和任务描述列表
        previous_data_path = os.path.join(root_path, str(month - 1))
        latest_folder = find_latest_folder(previous_data_path)
        if latest_folder:
            print(f"最大数字文件夹路径是: {latest_folder}")
            previous_data_path = os.path.join(previous_data_path, latest_folder)
        else:
            print("未找到符合条件的数字文件夹")
            return '空', None
    else:
        previous_data_path = os.path.join(root_path, str(month), str(week - 1))
    previous_data = load_json(previous_data_path)
    relative = remove_root(previous_data_path, root_path)
    return previous_data, relative


def save_current_data(current_data, department_name, month, week):
    current_data_output_path = os.path.join(parent_dir, 'history', department_name, str(month))
    if week != 0:
        # 获取本部门上月任务列表和任务描述列表
        current_data_output_path = os.path.join(current_data_output_path, str(week))
    save_json(current_data, current_data_output_path)


def main(excel_path, pdf_path, department, month, week):
    # 获取重点任务列表
    important_works = extract_important_works(excel_path)
    # 提取部门
    department_name = extract_department_name(pdf_path)
    # 提取当月月报任务列表和任务描述列表
    tasks, task_statuses = extract_meeting_report(pdf_path)
    # print(tasks)
    # print(task_statuses)
    # 漏项检测
    missing_data = check_missing_items(tasks, important_works)
    # 描述模糊检测
    vague_data = analyze_task_status_descriptions(tasks, task_statuses)
    # 报告生成
    pdf_directory = os.path.dirname(pdf_path)  # 获取pdf_path的目录部分
    file_name_without_ext = os.path.splitext(os.path.basename(pdf_path))[0]
    # 构建输出文件路径，保存至pdf_path所在目录
    if week == 0:
        output_filename = f"{department_name}{month}月检测报告.xlsx"
    else:
        output_filename = f"{department_name}{month}月第{week}周检测报告.xlsx"
    output_file = os.path.join(pdf_directory, output_filename)
    add_sheet_to_excel(missing_data, output_file, '任务漏项检测结果')
    add_sheet_to_excel(vague_data, output_file, '任务开展情况描述模糊检测结果')

    print(f"报告已生成：{output_file}")

    # 保存当前数据
    current_data = dict(zip(tasks, task_statuses))
    # print(current_data)
    save_current_data(current_data, department_name, month, week)
    # 获取本部门之前的任务列表和任务描述列表数据
    previous_data, relative = get_previous_data(department_name, month, week)
    result = {"department_name": department_name, "output_filename": output_filename, "current_data": current_data,
              "previous_data": previous_data}

    print(result)
    return result


@blueprint.route("/start", methods=['POST'])
def start_main():
    # 获取JSON请求数据
    data = request.get_json()
    # todo 做校验
    keys = ['urls', 'month', 'week']
    # 检查是否提供 urls 参数且为列表
    if not data or 'urls' not in data or not isinstance(data['urls'], list):
        return jsonify({"error": "缺少必要的参数或格式错误: urls (应为字符串列表)"}), 400

    file_urls = data['urls']
    month = int(data['month'])
    week = data['week']
    week = int(week)
    results = []
    for file_url in file_urls:
        pdf_paths, uuid = get_file_from_url(file_url)
        excel_path = './data/重点任务清单（名称）.xlsx'
        print(pdf_paths)
        department = ''
        # week = 0
        result = main(excel_path, pdf_paths, department, month, week)

        file_name = result.pop("output_filename")
        department = result.get("department_name")
        # 读取 ip.txt 文件中的 context 值
        ip_file_path = os.path.join(parent_dir, 'config/ip.txt')
        try:
            with open(ip_file_path, 'r', encoding='utf-8') as f:
                context = f.read().strip()
        except Exception as e:
            print(f"读取 IP 文件失败: {e}")
            return jsonify({"error": "读取 IP 文件失败"}), 500

        file_url_result = context + uuid + "/" + file_name

        results.append({
            "result": result,
            "file_name": file_name,
            "file_url": file_url_result,
            "department": department,
            "file_id": uuid
        })

    return jsonify({
        "message": "处理完成",
        "result_list": results
    })


@blueprint.route("/update_ip", methods=['POST'])
def update_ip():
    """
    接口说明：通过 POST 请求更新 ip.txt 文件的内容。

    请求格式：
    {
        "ip": "新的IP地址"
    }

    返回值：
    - 成功时返回 {"message": "IP 更新成功"}
    - 缺少参数时返回 {"error": "缺少必要的参数: ip"} 且状态码为 400
    """
    data = request.get_json()

    # 检查是否提供 ip 参数
    if not data or 'ip' not in data:
        return jsonify({"error": "缺少必要的参数: ip"}), 400

    new_ip = data['ip']
    ip_file_path = os.path.join(parent_dir, 'config/ip.txt')  # ip.txt 的完整路径

    try:
        # 写入新 IP 到 ip.txt 文件
        with open(ip_file_path, 'w', encoding='utf-8') as f:
            f.write(new_ip)

        print(f"成功更新 IP 地址为: {new_ip}")
        return jsonify({"message": "IP 更新成功"})
    except Exception as e:
        print(f"写入文件失败: {e}")
        return jsonify({"error": "写入文件失败"}), 500


@blueprint.route("/getfilepost", methods=['POST'])
def download_file():
    data = request.get_json()
    path1 = data["file_id"]
    filename = data["file_name"]
    # 假设文件存储在某个目录下，例如：'./downloads/'
    file_directory = './pdf/'

    # 拼接文件路径
    file_path = os.path.join(file_directory, path1, f"{filename}")

    # 检查文件是否存在
    if not os.path.isfile(file_path):
        return jsonify({"error": "文件不存在"}), 404

    # 使用 send_file 发送文件给客户端
    return send_file(file_path, as_attachment=True)


@blueprint.route("/getfileurl/<string:path1>/<string:filename>", methods=['GET'])
def download_files(path1, filename):
    # 假设文件存储在某个目录下，例如：'./downloads/'
    file_directory = './pdf/'

    # 拼接文件路径
    file_path = os.path.join(file_directory, path1, f"{filename}")

    # 检查文件是否存在
    if not os.path.isfile(file_path):
        return jsonify({"error": "文件不存在"}), 404

    # 使用 send_file 发送文件给客户端
    return send_file(file_path, as_attachment=True)


@blueprint.route("/delete_folder", methods=['POST'])
def delete_folder():
    """
    接口说明：通过 POST 请求删除指定名称的文件夹。

    请求格式：
    - folder_names: 文件夹名称列表（位于 ./pdf/ 目录下）

    返回值：
    - 成功时返回 {"message": "文件夹删除成功"}
    - 文件夹不存在时跳过并提示
    - 删除失败时返回错误信息和状态码 500
    """
    data = request.get_json()
    folder_names = data.get("folder_names")  # 获取文件夹名称列表

    folder_directory = './pdf/'  # 文件夹所在目录
    deleted_folders = []  # 存储成功删除的文件夹名
    not_found_folders = []  # 存储未找到的文件夹名

    try:
        # 如果没有提供 folder_names 或为空列表，则清空整个 pdf 目录
        if not folder_names or not isinstance(folder_names, list):
            # 清空整个 pdf 目录
            for item in os.listdir(folder_directory):
                item_path = os.path.join(folder_directory, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)  # 删除子目录
                else:
                    os.remove(item_path)  # 删除文件
            print(f"成功清空目录: {folder_directory}")
            return jsonify({"message": "目录已清空"})

        # 遍历要删除的每个文件夹
        for folder_name in folder_names:
            folder_path = os.path.join(folder_directory, folder_name)

            # 检查文件夹是否存在
            if not os.path.isdir(folder_path):
                not_found_folders.append(folder_name)
                continue

            # 递归删除文件夹及其内容
            shutil.rmtree(folder_path)
            deleted_folders.append(folder_name)
            print(f"成功删除文件夹: {folder_path}")

        # 构造响应结果
        result = {}
        if deleted_folders:
            result["message"] = "以下文件夹已成功删除"
            result["deleted_folders"] = deleted_folders
        if not_found_folders:
            result["not_found_folders"] = not_found_folders
            result["warning"] = "部分文件夹未找到"

        return jsonify(result)

    except Exception as e:
        print(f"删除文件夹失败: {e}")
        return jsonify({"error": "删除文件夹失败"}), 500


@blueprint.route("/delete_history_folder", methods=['POST'])
def delete_history_folder():
    """
    接口说明：通过 POST 请求删除指定名称的文件夹。

    请求格式：
    - folder_names: 文件夹名称列表（位于 ./pdf/ 目录下）

    返回值：
    - 成功时返回 {"message": "文件夹删除成功"}
    - 文件夹不存在时跳过并提示
    - 删除失败时返回错误信息和状态码 500
    """
    data = request.get_json()

    folder_directory = './history/'  # 文件夹所在目录

    # 清空整个 pdf 目录
    for item in os.listdir(folder_directory):
        item_path = os.path.join(folder_directory, item)
        if os.path.isdir(item_path):
            shutil.rmtree(item_path)  # 删除子目录
        else:
            os.remove(item_path)  # 删除文件
    print(f"成功清空目录: {folder_directory}")
    return jsonify({"message": "目录已清空"})


@blueprint.route("/mdtodoc", methods=['POST'])
def md2word():
    # 获取请求参数
    data = request.get_json()
    md_text = data.get("md_text")
    file_id = data.get("file_id")
    doc_name = data.get("doc_name")

    if not md_text:
        raise ValueError("Missing md_text parameter")

    try:
        result_content = ""
        think_content = ""
        if validate_think_type_model(md_text):
            if validate_details_tags(md_text):
                think_content, result_content = clean_think_content(md_text)
            if validate_think_tags(md_text):
                think_content, result_content = clean_think_content_think_type(md_text)
        else:
            result_content = md_text

        html = markdown.markdown(text=result_content, extensions=["extra", "toc"])

        # 生成初始的Word文档内容
        output_buf = html2docx(html, title="My Document")
        output_buf.seek(0)  # 将指针移到文件开头以便读取

        # 加载文档并修改样式
        from docx import Document
        from docx.shared import Pt, Cm, RGBColor
        from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
        from docx.oxml.ns import qn
        from io import BytesIO

        doc = Document(output_buf)

        # 定义样式修改函数
        def modify_styles(doc):
            # 设置一级标题样式
            heading1 = doc.styles['Heading 1']
            heading1.font.size = Pt(22)  # 二号
            heading1.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            heading1.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            heading1.paragraph_format.line_spacing = Pt(30)

            # 设置中文字体
            def set_chinese_font(style, font_name):
                style.font.name = font_name
                rpr = style.element.get_or_add_rPr()
                rfonts = rpr.get_or_add_rFonts()
                style.font.color.rgb = RGBColor(0, 0, 0)
                rfonts.set(qn("w:eastAsia"), font_name)

            set_chinese_font(heading1, '方正小标宋_GBK')

            # 设置二级标题样式
            heading2 = doc.styles['Heading 2']
            heading2.font.size = Pt(15)  # 小三号
            heading2.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
            heading2.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            heading2.paragraph_format.line_spacing = Pt(30)
            set_chinese_font(heading2, '方正小标宋_GBK')

            # 设置二级标题样式
            heading3 = doc.styles['Heading 3']
            heading3.font.size = Pt(15)  # 小三号
            heading3.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
            heading3.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            heading3.paragraph_format.line_spacing = Pt(30)
            set_chinese_font(heading3, '方正小标宋_GBK')

            # 设置正文样式
            normal = doc.styles['Normal']
            normal.font.size = Pt(16)  # 三号
            normal.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            normal.paragraph_format.line_spacing = Pt(30)
            normal.paragraph_format.first_line_indent = Cm(0.74)  # 首行缩进2字符
            set_chinese_font(normal, '仿宋GB2312')

        # 应用样式修改
        modify_styles(doc)

        # 构建输出路径
        file_directory = os.path.join('./pdf', file_id)
        os.makedirs(file_directory, exist_ok=True)
        file_path = os.path.join(file_directory, "document.docx")

        # 保存 Word 文件
        doc.save(file_path)

        # 构造下载 URL
        ip_file_path = os.path.join(parent_dir, 'config/ip.txt')
        try:
            with open(ip_file_path, 'r', encoding='utf-8') as f:
                context = f.read().strip()
        except Exception as e:
            return jsonify({"error": "读取 IP 文件失败"}), 500

        download_url = context + file_id + "/" + doc_name

        return jsonify({
            "message": "文件已成功生成并保存",
            "file_url": download_url,
            "file_id": file_id,
            "file_name": "document.docx"
        })

    except Exception as e:
        print(f"生成 Word 文件失败: {e}")
        return jsonify({"error": "生成 Word 文件失败"}), 500